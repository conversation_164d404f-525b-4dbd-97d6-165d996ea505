const express = require('express');
const router = express.Router();
const gameController = require('../controllers/gameController');
const reviewController = require('../controllers/reviewController');
const { authenticateToken, optionalAuth } = require('../middleware/authMiddleware');

// Protected routes - place these BEFORE routes with path parameters
router.get('/my-uploads', authenticateToken, gameController.getUserUploads); 

// Game by slug route - make sure this comes before the /:id route to avoid conflicts
router.get('/by-slug/:slug', gameController.getGameBySlug);

// Public routes
router.get('/', gameController.getAllGames);
router.get('/genres', gameController.getGenres);
router.get('/tags', gameController.getTags);
router.get('/:id', gameController.getGameById);

// Reviews
router.get('/:gameId/reviews', optionalAuth, reviewController.getGameReviews);
router.post('/:gameId/reviews', authenticateToken, reviewController.submitReview);

// Game interactions (like, dislike, favorite)
router.post('/:gameId/like', authenticateToken, gameController.likeGame);
router.post('/:gameId/dislike', authenticateToken, gameController.dislikeGame);
router.post('/:gameId/favorite', authenticateToken, gameController.toggleFavorite);
router.get('/:gameId/interactions', optionalAuth, gameController.getGameInteractions);

module.exports = router;
