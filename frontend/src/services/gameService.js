// Import the properly configured API client that handles HTTPS correctly
import api from './api';

// Get all games with optional filters
export const getAllGames = async (filters = {}) => {
  try {
    let url = '/games';
    
    // Add query parameters for filters
    const params = new URLSearchParams();
    if (filters.genre) params.append('genre', filters.genre);
    if (filters.tag) params.append('tag', filters.tag);
    if (filters.search) params.append('search', filters.search);
    if (filters.sort) params.append('sort', filters.sort);
    if (filters.page) params.append('page', filters.page);
    if (filters.limit) params.append('limit', filters.limit);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    const response = await api.get(url); // Use the properly configured API client
    return response.data;
  } catch (error) {
    console.error('Error fetching games:', error);
    throw error.response?.data || { message: 'Error fetching games' };
  }
};

// Get game by ID including files data
export const getGameById = async (id) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.get(`/games/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching game by ID:', error);
    throw error;
  }
};

// Get game by slug for path-based access
export const getGameBySlug = async (slug) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.get(`/games/by-slug/${slug}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching game by slug:', error);
    throw error;
  }
};

// Get reviews for a game
export const getGameReviews = async (gameId) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.get(`/reviews/game/${gameId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching game reviews:', error);
    return [];
  }
};

// Submit a review for a game
export const submitGameReview = async (gameId, reviewData) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.post(`/reviews/game/${gameId}`, reviewData);
    return response.data;
  } catch (error) {
    console.error('Error submitting game review:', error);
    throw error;
  }
};

// Delete a review
export const deleteGameReview = async (reviewId) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.delete(`/reviews/${reviewId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting review:', error);
    throw error;
  }
};

// Send a reaction to a review (like/dislike)
export const sendReviewReaction = async (reviewId, reactionType) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.post(`/reviews/${reviewId}/reactions`, 
      { reactionType }
    );
    return response.data;
  } catch (error) {
    console.error('Error sending review reaction:', error);
    throw error;
  }
};

// Get comments for a specific review
export const getReviewComments = async (reviewId) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.get(`/reviews/${reviewId}/comments`);
    return response.data;
  } catch (error) {
    console.error('Error fetching review comments:', error);
    throw error;
  }
};

// Submit a comment on a review
export const submitReviewComment = async (reviewId, content) => {
  try {
    // Use the API client instead of direct axios
    const response = await api.post(
      `/reviews/${reviewId}/comments`, 
      { content }
    );
    return response.data;
  } catch (error) {
    console.error('Error submitting comment:', error.response?.data || error.message);
    throw error;
  }
};

// Get file download URL (for tracking downloads)
export const getDownloadUrl = async (fileId) => {
  try {
    const response = await api.get(`/games/files/${fileId}/download`); // Use the properly configured API client
    return response.data.downloadUrl;
  } catch (error) {
    console.error(`Error getting download URL for file ${fileId}:`, error);
    throw error.response?.data || { message: `Error getting download URL` };
  }
};

// Game interaction functions (like, dislike, favorite)
export const likeGame = async (gameId) => {
  try {
    const response = await api.post(`/games/${gameId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error liking game:', error);
    throw error.response?.data || { message: 'Error liking game' };
  }
};

export const dislikeGame = async (gameId) => {
  try {
    const response = await api.post(`/games/${gameId}/dislike`);
    return response.data;
  } catch (error) {
    console.error('Error disliking game:', error);
    throw error.response?.data || { message: 'Error disliking game' };
  }
};

export const toggleFavoriteGame = async (gameId) => {
  try {
    const response = await api.post(`/games/${gameId}/favorite`);
    return response.data;
  } catch (error) {
    console.error('Error toggling favorite:', error);
    throw error.response?.data || { message: 'Error toggling favorite' };
  }
};

export const getGameInteractions = async (gameId) => {
  try {
    const response = await api.get(`/games/${gameId}/interactions`);
    return response.data;
  } catch (error) {
    console.error('Error getting game interactions:', error);
    throw error.response?.data || { message: 'Error getting game interactions' };
  }
};

export default {
  getAllGames,
  getGameById,
  getGameBySlug,
  getGameReviews,
  submitGameReview,
  deleteGameReview,
  getDownloadUrl,
  sendReviewReaction,
  getReviewComments,
  submitReviewComment,
  likeGame,
  dislikeGame,
  toggleFavoriteGame,
  getGameInteractions
};
