import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { gamePlaceholder } from '../assets/placeholders.js';
import { getSecureImageUrl } from '../utils/imageUtils';
import { handleImageError } from '../utils/mediaUtils';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';

// Game Status Label Component
const GameStatusLabel = ({ status, className = "" }) => {
  if (!status) return null;

  const statusConfig = {
    'NEW': {
      text: 'NEW',
      className: 'bg-gradient-to-r from-green-500 to-green-600 text-white'
    },
    'UPDATED': {
      text: 'UPDATED',
      className: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white'
    },
    'HOT': {
      text: 'HOT',
      className: 'bg-gradient-to-r from-red-500 to-red-600 text-white'
    },
    'FEATURED': {
      text: 'FEATURED',
      className: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white'
    }
  };

  const config = statusConfig[status.toUpperCase()];
  if (!config) return null;

  return (
    <div className={`absolute top-2 left-2 z-20 px-2 py-1 rounded-md text-xs font-bold uppercase tracking-wide shadow-lg ${config.className} ${className}`}>
      {config.text}
    </div>
  );
};

GameStatusLabel.propTypes = {
  status: PropTypes.string,
  className: PropTypes.string
};

// Use the proper placeholder image
const FALLBACK_IMAGE = gamePlaceholder;

/**
 * GameCard Component - Displays a game card with hover effects and animations
 * @param {Object} game - Game object containing all game information
 * @param {string} size - Size variant: 'small', 'medium', 'large', 'featured'
 * @param {string} className - Additional CSS classes
 */
const GameCard = ({ game, size = 'medium', className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [gifLoaded, setGifLoaded] = useState(false);
  const [imgError, setImgError] = useState(false);
  const preloadedGifRef = useRef(null);
  const { createLanguageLink } = useLanguageNavigation();

  // Size configurations
  const sizeConfig = {
    compact: {
      cardClass: 'h-[130px]', // 130px height
      imageClass: 'h-full', // Full height for compact
      titleClass: 'text-sm font-semibold',
      contentClass: 'absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200',
      showDetails: false, // Hide genre, price, tags for compact view
      showTitleOnHover: true // Show title only on hover
    },
    small: {
      cardClass: 'h-64',
      imageClass: 'pt-[56.25%]', // 16:9 aspect ratio
      titleClass: 'text-sm',
      contentClass: 'p-3',
      showDetails: true
    },
    medium: {
      cardClass: 'h-80',
      imageClass: 'pt-[56.25%]', // 16:9 aspect ratio
      titleClass: 'text-lg',
      contentClass: 'p-4',
      showDetails: true
    },
    large: {
      cardClass: 'h-96',
      imageClass: 'pt-[56.25%]', // 16:9 aspect ratio
      titleClass: 'text-xl',
      contentClass: 'p-5',
      showDetails: true
    },
    featured: {
      cardClass: 'h-80 w-80',
      imageClass: 'pt-[75%]', // 4:3 aspect ratio for featured
      titleClass: 'text-xl',
      contentClass: 'p-5',
      showDetails: true
    }
  };

  const currentSize = sizeConfig[size] || sizeConfig.medium;
  
  // Function to determine price display based on payment type
  const getPriceDisplay = () => {
    if (game.paymentType === 'free') {
      return <span className="px-3 py-1 text-sm font-semibold rounded-full bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-[0_2px_8px_rgba(34,197,94,0.4)]">FREE</span>;
    } else if (game.paymentType === 'credits') {
      return <span className="px-3 py-1 text-sm font-semibold rounded-full bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] text-white shadow-[0_2px_8px_rgba(74,101,255,0.4)]">FREE/CREDITS</span>;
    } else {
      return <span className="px-3 py-1 text-sm font-semibold rounded-full bg-gradient-to-r from-[#f44336] to-[#ff9800] text-white shadow-[0_2px_8px_rgba(244,67,54,0.4)]">{game.price}</span>;
    }
  };

  // Preload the GIF when component mounts (only if hoverGif exists)
  useEffect(() => {
    if (game.hoverGif) {
      const img = new Image();
      img.onload = () => {
        setGifLoaded(true);
        preloadedGifRef.current = game.hoverGif;
      };
      img.src = game.hoverGif;
      
      return () => {
        img.onload = null; // Cleanup
      };
    }
  }, [game.hoverGif]);

  // Handle hover state with slight delay to avoid flashing
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const imageUrl = (() => {
    // If we already tried loading the image and got an error, use fallback immediately
    if (imgError) return FALLBACK_IMAGE;
    
    // Use mediaUtils to handle URL transformation across environments
    const imagePath = game.imageUrl || game.image || '';
    
    // If no image is provided at all, use fallback
    if (!imagePath) return FALLBACK_IMAGE;
    
    // Transform URL for the appropriate environment (S3 in production, local in development)
    return getSecureImageUrl(imagePath);
  })();

  // Determine the appropriate URL for the game (now using path-based routing)
  const getGameUrl = () => {
    // Get game slug
    const slug = game.slug || game.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    // Use language-aware routing
    return createLanguageLink(`/${slug}`);
  };

  return (
    <a
      href={getGameUrl()}
      className={`group bg-gradient-to-br from-[#1a1a1a] to-[#0d0d0d] rounded-xl overflow-hidden transition-none relative ${currentSize.cardClass} ${size === 'compact' ? 'block' : 'flex flex-col'} cursor-pointer no-underline text-inherit hover:after:shadow-[inset_0_0_0_2px_rgba(255,69,0,0.6)] after:content-[''] after:absolute after:inset-0 after:rounded-xl after:shadow-[inset_0_0_0_2px_rgba(255,69,0,0)] after:transition-shadow after:duration-300 after:pointer-events-none ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Game Status Label */}
      <GameStatusLabel status={game.status} />
      {/* Full card GIF overlay on hover */}
      <div 
        className={`absolute inset-0 bg-black/40 flex items-center justify-center z-10 rounded-xl overflow-hidden transition-none ${isHovered && game.hoverGif ? 'opacity-100 visible' : 'opacity-0 invisible'}`}
      >
        {gifLoaded && game.hoverGif && (
          <img 
            src={getSecureImageUrl(game.hoverGif)} 
            alt={`${game.title} gameplay`} 
            className={`absolute inset-0 w-full h-full object-cover z-[11] ${gifLoaded ? 'opacity-100' : 'opacity-0'} transition-none`}
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        )}
        {isHovered && game.hoverGif && !gifLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/40 z-[11]">
            <div className="w-10 h-10 border-4 border-[rgba(255,69,0,0.3)] rounded-full border-t-[#ff4500] animate-spin"></div>
          </div>
        )}
      </div>
      
      {/* Image Container */}
      {size === 'compact' ? (
        <div className={`relative w-full ${currentSize.imageClass} overflow-hidden`}>
          <img
            className="absolute inset-0 w-full h-full object-cover transition-none"
            src={imageUrl}
            alt={game.title}
            onError={(e) => {
              setImgError(true);
              handleImageError(e, FALLBACK_IMAGE);
            }}
          />
        </div>
      ) : (
        <div className={`relative w-full h-0 ${currentSize.imageClass} overflow-hidden`}>
          <img
            className="absolute inset-0 w-full h-full object-cover transition-none"
            src={imageUrl}
            alt={game.title}
            onError={(e) => {
              setImgError(true);
              handleImageError(e, FALLBACK_IMAGE);
            }}
          />
        </div>
      )}

      {/* Title overlay for compact cards - shows on hover - positioned above GIF */}
      {size === 'compact' && (
        <div className={`${currentSize.contentClass} z-[30]`}>
          <div className="bg-black/80 backdrop-blur-sm rounded-md px-3 py-1.5 inline-block shadow-lg">
            <h3 className={`${currentSize.titleClass} text-white truncate max-w-[200px]`} title={game.title}>
              {game.title}
            </h3>
          </div>
        </div>
      )}

      {/* Content Container - only for non-compact cards */}
      {size !== 'compact' && (
        <div className={`${currentSize.contentClass} flex-grow flex flex-col relative z-[1]`}>
          <div className="mb-2">
            <h3 className={`${currentSize.titleClass} font-semibold text-white truncate`} title={game.title}>{game.title}</h3>
          </div>

          {currentSize.showDetails && (
            <>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-300">{game.genre}</span>
                {getPriceDisplay()}
              </div>

              <div className="flex flex-wrap gap-2">
                {game.tags && game.tags.slice(0, 3).map((tag, index) => (
                  <span key={index} className="px-2 py-1 text-xs bg-[#2a2a2a] text-gray-300 rounded-md">{tag}</span>
                ))}
              </div>
            </>
          )}
        </div>
      )}
    </a>
  );
};

GameCard.propTypes = {
  game: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    title: PropTypes.string.isRequired,
    genre: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    paymentType: PropTypes.string,
    imageUrl: PropTypes.string,
    image: PropTypes.string,
    hoverGif: PropTypes.string,
    slug: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    status: PropTypes.oneOf(['NEW', 'UPDATED', 'HOT', 'FEATURED'])
  }).isRequired,
  size: PropTypes.oneOf(['compact', 'small', 'medium', 'large', 'featured']),
  className: PropTypes.string
};

export default GameCard;
