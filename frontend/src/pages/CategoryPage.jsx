import { useState, useEffect, useCallback } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import GameCard from '../components/GameCard';
import Sidebar from '../components/Sidebar';
import GameLoader from '../components/GameLoader';
import { FaSortAmountDown } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../config/env.js';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';
import { getGameCategories, isValidCategorySlug } from '../utils/categoryUtils';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';

const CategoryPage = () => {
  const { t } = useLanguage();
  const { category } = useParams();
  const location = useLocation();
  const { isSidebarOpen, toggleSidebar } = useSidebar();
  const { getCurrentPathWithoutLanguage } = useLanguageNavigation();

  // Get path without language prefix for route determination
  const pathWithoutLanguage = getCurrentPathWithoutLanguage();

  // Determine if this is a category route or navigation route
  const isCategoryRoute = pathWithoutLanguage.startsWith('/category/');
  const isNavigationRoute = !isCategoryRoute;
  
  // Games data
  const [games, setGames] = useState([]);
  const [filteredGames, setFilteredGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Sorting
  const [sortOption, setSortOption] = useState('newest');
  const sortOptions = {
    newest: 'Newest',
    oldest: 'Oldest',
    popular: 'Most Popular',
    downloads: 'Most Downloaded',
    alphabetical: 'Name (A-Z)',
  };

  const fetchGames = useCallback(async () => {
    setLoading(true);
    setError(null);
    // Clear existing games when fetching new data to prevent showing stale data
    setGames([]);
    setFilteredGames([]);

    try {
      let apiUrl = `${API_URL}/games`;
      const params = new URLSearchParams();

      // Set a higher limit to get more games
      params.append('limit', '50');

      // Add current sort option (unless it's a navigation route that sets its own sort)
      const currentIsNavigationRoute = !pathWithoutLanguage.startsWith('/category/');
      if (!currentIsNavigationRoute || (currentIsNavigationRoute && sortOption !== 'newest')) {
        params.append('sort', sortOption);
      }

      // Determine filter type based on route
      if (isCategoryRoute) {
        // For category routes, we need the category parameter
        if (!category) {
          setLoading(false);
          setError('Category not found - missing parameter');
          return; // Exit early if category is not available
        }

        // Validate category exists in our JSON data
        if (!isValidCategorySlug(category)) {
          setLoading(false);
          setError(`Category "${category}" not found`);
          return;
        }

        // Category-based filtering (by genre)
        // Create category mapping from JSON data - use 'value' instead of 'label' for database compatibility
        const categoryToGenreMap = {};
        getGameCategories().forEach(cat => {
          categoryToGenreMap[cat.slug] = cat.value; // Use 'value' (lowercase) instead of 'label' (capitalized)
        });

        const genreName = categoryToGenreMap[category] || category;

        if (category === '2-player') {
          params.append('tag', 'multiplayer');
        } else {
          params.append('genre', genreName);
        }
      } else if (isNavigationRoute) {
        // Navigation-based filtering (by special criteria)
        switch (pathWithoutLanguage) {
          case '/new':
            params.append('sort', 'newest');
            break;
          case '/trending':
            params.append('sort', 'popular');
            break;
          case '/updated':
            // For updated, we'll sort by newest but could add a specific updated_at field later
            params.append('sort', 'newest');
            break;
          case '/multiplayer':
            params.append('tag', 'multiplayer');
            break;
          case '/originals':
            params.append('tag', 'original');
            break;
          case '/recently-played':
            // This would require user authentication and play history
            // For now, show newest games
            params.append('sort', 'newest');
            break;
          default:
            break;
        }
      }

      if (params.toString()) {
        apiUrl += `?${params.toString()}`;
      }

      const response = await axios.get(apiUrl);
      const gamesArray = response.data.games || [];
      
      // Transform the database data to match the expected format for GameCard
      const formattedGames = gamesArray.map((game, index) => {
        // Determine game status based on release date and other factors
        let status = null;
        const releaseDate = new Date(game.releaseDate);
        const now = new Date();
        const daysSinceRelease = (now - releaseDate) / (1000 * 60 * 60 * 24);

        if (daysSinceRelease <= 7) {
          status = 'NEW';
        } else if (daysSinceRelease <= 30 && game.lastUpdated) {
          const lastUpdated = new Date(game.lastUpdated);
          const daysSinceUpdate = (now - lastUpdated) / (1000 * 60 * 60 * 24);
          if (daysSinceUpdate <= 7) {
            status = 'UPDATED';
          }
        }

        // Mark some games as HOT based on popularity (placeholder logic)
        if (index < 3 && !status) {
          status = 'HOT';
        }

        return {
          id: game.id,
          title: game.title,
          description: game.description,
          image: game.cardImage || game.image,
          hoverGif: game.animationGif,
          genre: game.genre,
          tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
          paymentType: game.priceModel,
          price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` :
                 game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
          releaseDate: releaseDate,
          status: status
        };
      });
      
      setGames(formattedGames);
    } catch (err) {
      console.error('Error fetching games:', err);
      setError('Failed to load games. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [category, pathWithoutLanguage, sortOption, isCategoryRoute]);

  // Clear games immediately when category or route changes
  useEffect(() => {
    // Clear previous data to prevent showing stale games
    setGames([]);
    setFilteredGames([]);
    setError(null);
  }, [category, pathWithoutLanguage]);

  // Fetch games based on category and sort option
  useEffect(() => {
    // For category routes, only fetch if we have the category parameter
    // For navigation routes, we can fetch immediately
    if (isCategoryRoute && (!category || !isValidCategorySlug(category))) {
      setLoading(false);
      setError(category ? `Category "${category}" not found` : 'Category not found - missing parameter');
      return;
    }

    fetchGames();
  }, [category, pathWithoutLanguage, sortOption, fetchGames, isCategoryRoute]);

  // Apply filters and sorting
  useEffect(() => {
    if (games.length === 0) return;

    let results = [...games];

    // Apply sorting
    results = sortGames(results);

    setFilteredGames(results);
  }, [games, sortOption]);

  // Sort games based on selected option 
  const sortGames = (gamesArray) => {
    // The API already returns sorted data for backend-supported sorts
    // So we just return as-is
    return gamesArray;
  };

  // Get page title based on route
  const getPageTitle = () => {
    if (isCategoryRoute) {
      // For category routes, we need the category parameter
      if (!category) {
        return 'Games'; // Fallback title
      }

      // Create translation mapping from JSON data
      const categoryToTranslationMap = {};
      getGameCategories().forEach(cat => {
        categoryToTranslationMap[cat.slug] = cat.value;
      });

      const translationKey = categoryToTranslationMap[category];
      if (translationKey) {
        return t(`sidebar.navigation.${translationKey}`);
      }

      // Fallback to formatted category name
      return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
    }

    if (isNavigationRoute) {
      const pathMap = {
        '/new': t('sidebar.navigation.new'),
        '/trending': t('sidebar.navigation.trendingNow'),
        '/updated': t('sidebar.navigation.updated'),
        '/multiplayer': t('sidebar.navigation.multiplayer'),
        '/originals': t('sidebar.navigation.originals'),
        '/recently-played': t('sidebar.navigation.recentlyPlayed')
      };

      return pathMap[pathWithoutLanguage] || 'Games';
    }

    return 'Games'; // Default fallback
  };

  // Render loading state
  if (loading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 ${isSidebarOpen ? '' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <GameLoader size="lg" variant="dice" />
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 ${isSidebarOpen ? '' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <div className="text-red-500 text-xl">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
      <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
      
      <div className={`flex-1 ${isSidebarOpen ? '' : 'lg:pl-0'}`}>
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-2">
          <div className="flex gap-4">
            <h1 className="text-3xl font-bold text-white">{getPageTitle()}</h1>            
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-2">
                <FaSortAmountDown className="text-gray-400" />
                <select 
                  value={sortOption} 
                  onChange={(e) => setSortOption(e.target.value)}
                  className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500 transition-colors duration-200"
                >
                  {Object.entries(sortOptions).map(([value, label]) => (
                    <option key={value} value={value}>{label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Games Grid */}
          {filteredGames.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <h3 className="text-xl font-bold text-white mb-2">No games found</h3>
              <p className="text-gray-400">Try adjusting your search or check back later</p>
            </div>
          ) : (
            <div className="flex flex-wrap gap-4">
              {filteredGames.map(game => (
                <div key={game.id} className="w-[230px] h-[130px]">
                  <GameCard 
                    game={game} 
                    size="compact"
                    className="h-full rounded-lg overflow-hidden"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryPage;
